[package]
name = "moonfield-graphics"
version = "0.1.0"
edition = "2024"

[dependencies]
# Cross-platform dependencies
ash = { workspace = true }
ash-window = { workspace = true }
winit = { workspace = true }
tracing = { workspace = true }

# macOS/iOS specific dependencies - only included when metal feature is enabled
objc2 = { workspace = true, optional = true }
objc2-app-kit = { workspace = true, optional = true }
objc2-core-foundation = { workspace = true, optional = true }
objc2-metal = { workspace = true, optional = true }
objc2-quartz-core = { workspace = true, optional = true }

[features]
default = []
# Backend features
metal = ["dep:objc2", "dep:objc2-app-kit", "dep:objc2-core-foundation", "dep:objc2-metal", "dep:objc2-quartz-core"]
vulkan = []

# Moonfield
A modern graphics rendering engine written in Rust, serving as an exploration ground for advanced graphics programming techniques and high-performance rendering systems. 

🚧 **Status**: Active development - Everything is under construction. And this is just a playground for myself.

## ✨ Planned Features

- **Multi-Backend Support**: Primarily Metal and Vulkan backends
- **Clean Architecture Design**: Simple yet sufficient architecture  
- **Modern Graphics Pipeline**: GPU-driven pipeline, bindless rendering, mesh shading, variable rate shading etc.
- **Advanced Rendering**: Real-time ray tracing and global illumination etc.

## 💻 Hardware Environment

This is a personal project optimized for my development setup:
- **Primary GPU**: Apple M4, AMD RX 9070XT
- **Graphics Backend**: Metal (Current focus), Vulkan

Portability is not a priority.

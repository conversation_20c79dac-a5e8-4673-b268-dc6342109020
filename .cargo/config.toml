[alias]
# Platform-specific example builds
run-native = [
    "run",
    "--example",
    "moonfield_window",
    "--target-dir",
    "target/native",
]
build-native = [
    "build",
    "--example",
    "moonfield_window",
    "--target-dir",
    "target/native",
]
check-native = ["check", "--target-dir", "target/native"]
clean-native = ["clean", "--target-dir", "target/native"]

generate-xcode = ["xcode", "--manifest-path", "platform/xcode/Cargo.toml", "--output-dir", "platform/xcode"]

[build]
# Default target directory for workspace builds
target-dir = "target/workspace"

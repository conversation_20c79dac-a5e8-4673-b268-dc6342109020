[package]
name = "moonfield"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["staticlib", "rlib"]

# Library dependencies
[dependencies]
moonfield-impl = { path = "../moonfield-impl" }
moonfield-core = { path = "../moonfield-core" }
moonfield-graphics = { path = "../moonfield-graphics" }

[features]
default = []
# Backend features
metal = ["moonfield-graphics/metal", "moonfield-impl/metal"]
vulkan = ["moonfield-graphics/vulkan", "moonfield-impl/vulkan"]

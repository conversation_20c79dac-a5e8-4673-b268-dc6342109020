use std::rc::Rc;

use moonfield_core::math::{Mat4, Vec3, Point3, color::Color};
use moonfield_graphics::{
    backend::{GraphicsBackend, SharedGraphicsBackend},
    error::GraphicsError,
};

use crate::engine::error::EngineError;

pub struct Renderer {
    frame_size: (u32, u32),
    pub backend: SharedGraphicsBackend,

    // Camera matrices
    view_matrix: Mat4,
    projection_matrix: Mat4,

    // Camera parameters
    camera_position: Point3,
    camera_target: Point3,
    camera_up: Vec3,

    // Projection parameters
    fov: f32,
    near_plane: f32,
    far_plane: f32,

    // Clear color
    clear_color: Color,
}

impl Renderer {
    pub fn new(
        backend: Rc<dyn GraphicsBackend>, frame_size: (u32, u32),
    ) -> Result<Self, EngineError> {
        use moonfield_core::math::{utils, color};

        // Default camera setup
        let camera_position = Point3::new(0.0, 0.0, 5.0);
        let camera_target = Point3::new(0.0, 0.0, 0.0);
        let camera_up = Vec3::new(0.0, 1.0, 0.0);

        // Default projection parameters
        let fov = utils::deg_to_rad(45.0);
        let aspect = frame_size.0 as f32 / frame_size.1 as f32;
        let near_plane = 0.1;
        let far_plane = 100.0;

        // Calculate matrices
        let view_matrix = utils::look_at(&camera_position, &camera_target, &camera_up);
        let projection_matrix = utils::perspective(fov, aspect, near_plane, far_plane);

        Ok(Self {
            frame_size,
            backend,
            view_matrix,
            projection_matrix,
            camera_position,
            camera_target,
            camera_up,
            fov,
            near_plane,
            far_plane,
            clear_color: color::BLACK,
        })
    }

    pub(crate) fn render_frame(&mut self) -> Result<(), GraphicsError> {
        let back_buffer = self.backend.back_buffer()?;

        // Use the renderer's clear color
        back_buffer.clear([
            self.clear_color.x,
            self.clear_color.y,
            self.clear_color.z,
            self.clear_color.w,
        ])?;

        drop(back_buffer);

        self.backend.swap_buffers()?;

        Ok(())
    }
    pub fn graphics_backend(&self) -> SharedGraphicsBackend {
        self.backend.clone()
    }

    pub(crate) fn set_frame_size(
        &mut self, new_size: (u32, u32),
    ) -> Result<(), GraphicsError> {
        self.frame_size.0 = new_size.0;
        self.frame_size.1 = new_size.1;

        // Update projection matrix with new aspect ratio
        let aspect = new_size.0 as f32 / new_size.1 as f32;
        self.projection_matrix = moonfield_core::math::utils::perspective(
            self.fov, aspect, self.near_plane, self.far_plane
        );

        self.graphics_backend().set_frame_size(new_size);

        Ok(())
    }

    /// Set the clear color for the renderer
    pub fn set_clear_color(&mut self, color: Color) {
        self.clear_color = color;
    }

    /// Get the current clear color
    pub fn clear_color(&self) -> Color {
        self.clear_color
    }

    /// Set the camera position and target
    pub fn set_camera(&mut self, position: Point3, target: Point3, up: Vec3) {
        self.camera_position = position;
        self.camera_target = target;
        self.camera_up = up;
        self.update_view_matrix();
    }

    /// Get the current camera position
    pub fn camera_position(&self) -> Point3 {
        self.camera_position
    }

    /// Get the current camera target
    pub fn camera_target(&self) -> Point3 {
        self.camera_target
    }

    /// Set the field of view (in radians)
    pub fn set_fov(&mut self, fov: f32) {
        self.fov = fov;
        self.update_projection_matrix();
    }

    /// Get the current field of view (in radians)
    pub fn fov(&self) -> f32 {
        self.fov
    }

    /// Set the near and far clipping planes
    pub fn set_clipping_planes(&mut self, near: f32, far: f32) {
        self.near_plane = near;
        self.far_plane = far;
        self.update_projection_matrix();
    }

    /// Get the view matrix
    pub fn view_matrix(&self) -> Mat4 {
        self.view_matrix
    }

    /// Get the projection matrix
    pub fn projection_matrix(&self) -> Mat4 {
        self.projection_matrix
    }

    /// Get the combined view-projection matrix
    pub fn view_projection_matrix(&self) -> Mat4 {
        self.projection_matrix * self.view_matrix
    }

    /// Update the view matrix based on current camera parameters
    fn update_view_matrix(&mut self) {
        self.view_matrix = moonfield_core::math::utils::look_at(
            &self.camera_position,
            &self.camera_target,
            &self.camera_up,
        );
    }

    /// Update the projection matrix based on current parameters
    fn update_projection_matrix(&mut self) {
        let aspect = self.frame_size.0 as f32 / self.frame_size.1 as f32;
        self.projection_matrix = moonfield_core::math::utils::perspective(
            self.fov, aspect, self.near_plane, self.far_plane
        );
    }
}

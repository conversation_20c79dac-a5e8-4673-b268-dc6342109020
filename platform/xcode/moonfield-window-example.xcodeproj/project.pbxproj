// !$*UTF8*$!
{
	/* generated with cargo-xcode 1.11.0 */
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 53;
	objects = {

/* Begin PBXBuildFile section */
		CA00FBD8F20D4581F30937FB /* Cargo.toml in Sources */ = {isa = PBXBuildFile; fileRef = CAF92D43DE713EF4668187A5 /* Cargo.toml */; settings = {COMPILER_FLAGS = "--bin 'moonfield_window'"; }; };
/* End PBXBuildFile section */

/* Begin PBXBuildRule section */
		CAF42D43DE71AC6C1400ACA8 /* PBXBuildRule */ = {
			isa = PBXBuildRule;
			compilerSpec = com.apple.compilers.proxy.script;
			dependencyFile = "$(DERIVED_FILE_DIR)/$(ARCHS)-$(EXECUTABLE_NAME).d";
			filePatterns = "*/Cargo.toml";
			fileType = pattern.proxy;
			inputFiles = (
			);
			isEditable = 0;
			name = "Cargo project build";
			outputFiles = (
				"$(TARGET_BUILD_DIR)/$(EXECUTABLE_NAME)",
			);
			runOncePerArchitecture = 0;
			script = "# generated with cargo-xcode 1.11.0\nset -euo pipefail;\nexport PATH=\"$HOME/.cargo/bin:$PATH:/usr/local/bin:/opt/homebrew/bin\";\n# don't use ios/watchos linker for build scripts and proc macros\nexport CARGO_TARGET_AARCH64_APPLE_DARWIN_LINKER=/usr/bin/ld\nexport CARGO_TARGET_X86_64_APPLE_DARWIN_LINKER=/usr/bin/ld\nexport NO_COLOR=1\n\ncase \"$PLATFORM_NAME\" in\n \"macosx\")\n  CARGO_XCODE_TARGET_OS=darwin\n  if [ \"${IS_MACCATALYST-NO}\" = YES ]; then\n   CARGO_XCODE_TARGET_OS=ios-macabi\n  fi\n  ;;\n \"iphoneos\") CARGO_XCODE_TARGET_OS=ios ;;\n \"iphonesimulator\") CARGO_XCODE_TARGET_OS=ios-sim ;;\n \"appletvos\" | \"appletvsimulator\") CARGO_XCODE_TARGET_OS=tvos ;;\n \"watchos\") CARGO_XCODE_TARGET_OS=watchos ;;\n \"watchsimulator\") CARGO_XCODE_TARGET_OS=watchos-sim ;;\n \"xros\") CARGO_XCODE_TARGET_OS=visionos ;;\n \"xrsimulator\") CARGO_XCODE_TARGET_OS=visionos-sim ;;\n *)\n  CARGO_XCODE_TARGET_OS=\"$PLATFORM_NAME\"\n  echo >&2 \"warning: cargo-xcode needs to be updated to handle $PLATFORM_NAME\"\n  ;;\nesac\n\nCARGO_XCODE_TARGET_TRIPLES=\"\"\nCARGO_XCODE_TARGET_FLAGS=\"\"\nLIPO_ARGS=\"\"\nfor arch in $ARCHS; do\n if [[ \"$arch\" == \"arm64\" ]]; then arch=aarch64; fi\n if [[ \"$arch\" == \"i386\" && \"$CARGO_XCODE_TARGET_OS\" != \"ios\" ]]; then arch=i686; fi\n triple=\"${arch}-apple-$CARGO_XCODE_TARGET_OS\"\n CARGO_XCODE_TARGET_TRIPLES+=\" $triple\"\n CARGO_XCODE_TARGET_FLAGS+=\" --target=$triple\"\n LIPO_ARGS+=\"$CARGO_TARGET_DIR/$triple/$CARGO_XCODE_BUILD_PROFILE/$CARGO_XCODE_CARGO_FILE_NAME\n\"\ndone\n\necho >&2 \"Cargo $CARGO_XCODE_BUILD_PROFILE $ACTION for $PLATFORM_NAME $ARCHS =$CARGO_XCODE_TARGET_TRIPLES; using ${SDK_NAMES:-}. \\$PATH is:\"\ntr >&2 : '\\n' <<<\"$PATH\"\n\nif command -v rustup &> /dev/null; then\n for triple in $CARGO_XCODE_TARGET_TRIPLES; do\n  if ! rustup target list --installed | grep -Eq \"^$triple$\"; then\n   echo >&2 \"warning: this build requires rustup toolchain for $triple, but it isn't installed (will try rustup next)\"\n   rustup target add \"$triple\" || {\n    echo >&2 \"warning: can't install $triple, will try nightly -Zbuild-std\";\n    OTHER_INPUT_FILE_FLAGS+=\" -Zbuild-std\";\n    if [ -z \"${RUSTUP_TOOLCHAIN:-}\" ]; then\n     export RUSTUP_TOOLCHAIN=nightly\n    fi\n    break;\n   }\n  fi\n done\nfi\n\nif [ \"$CARGO_XCODE_BUILD_PROFILE\" = release ]; then\n OTHER_INPUT_FILE_FLAGS=\"$OTHER_INPUT_FILE_FLAGS --release\"\nfi\n\nif [ \"$ACTION\" = clean ]; then\n cargo clean --verbose --manifest-path=\"$SCRIPT_INPUT_FILE\" $CARGO_XCODE_TARGET_FLAGS $OTHER_INPUT_FILE_FLAGS;\n rm -f \"$SCRIPT_OUTPUT_FILE_0\"\n exit 0\nfi\n\n{ cargo build --manifest-path=\"$SCRIPT_INPUT_FILE\" --features=\"${CARGO_XCODE_FEATURES:-}\" $CARGO_XCODE_TARGET_FLAGS $OTHER_INPUT_FILE_FLAGS --verbose --message-format=short 2>&1 | sed -E 's/^([^ :]+:[0-9]+:[0-9]+: error)/\\1: /' >&2; } || { echo >&2 \"$SCRIPT_INPUT_FILE: error: cargo-xcode project build failed; $CARGO_XCODE_TARGET_TRIPLES\"; exit 1; }\n\ntr '\\n' '\\0' <<<\"$LIPO_ARGS\" | xargs -0 lipo -create -output \"$SCRIPT_OUTPUT_FILE_0\"\n\nif [ ${LD_DYLIB_INSTALL_NAME:+1} ]; then\n install_name_tool -id \"$LD_DYLIB_INSTALL_NAME\" \"$SCRIPT_OUTPUT_FILE_0\"\nfi\n\nDEP_FILE_DST=\"$DERIVED_FILE_DIR/${ARCHS}-${EXECUTABLE_NAME}.d\"\necho \"\" > \"$DEP_FILE_DST\"\nfor triple in $CARGO_XCODE_TARGET_TRIPLES; do\n BUILT_SRC=\"$CARGO_TARGET_DIR/$triple/$CARGO_XCODE_BUILD_PROFILE/$CARGO_XCODE_CARGO_FILE_NAME\"\n\n # cargo generates a dep file, but for its own path, so append our rename to it\n DEP_FILE_SRC=\"$CARGO_TARGET_DIR/$triple/$CARGO_XCODE_BUILD_PROFILE/$CARGO_XCODE_CARGO_DEP_FILE_NAME\"\n if [ -f \"$DEP_FILE_SRC\" ]; then\n  cat \"$DEP_FILE_SRC\" >> \"$DEP_FILE_DST\"\n fi\n echo >> \"$DEP_FILE_DST\" \"${SCRIPT_OUTPUT_FILE_0/ /\\\\ /}: ${BUILT_SRC/ /\\\\ /}\"\ndone\ncat \"$DEP_FILE_DST\"\n\necho \"success: $ACTION of $SCRIPT_OUTPUT_FILE_0 for $CARGO_XCODE_TARGET_TRIPLES\"\n";
		};
/* End PBXBuildRule section */

/* Begin PBXFileReference section */
		CA00BD0ECD649217DD73837A /* moonfield_window */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = moonfield_window; sourceTree = BUILT_PRODUCTS_DIR; };
		CAF92D43DE713EF4668187A5 /* Cargo.toml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = Cargo.toml; path = Cargo.toml; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXGroup section */
		CAF02D43DE71D65BC3C892A8 = {
			isa = PBXGroup;
			children = (
				CAF92D43DE713EF4668187A5 /* Cargo.toml */,
				CAF12D43DE7122869D176AE5 /* Products */,
				CAF22D43DE7198AF0B5890DB /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		CAF12D43DE7122869D176AE5 /* Products */ = {
			isa = PBXGroup;
			children = (
				CA00BD0ECD649217DD73837A /* moonfield_window */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CAF22D43DE7198AF0B5890DB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CA00BD0ECD644581F30937FB /* moonfield_window (standalone executable) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CA00836429264581F30937FB /* Build configuration list for PBXNativeTarget "moonfield_window (standalone executable)" */;
			buildPhases = (
				CA0009C2BFEC4581F30937FB /* Sources */,
			);
			buildRules = (
				CAF42D43DE71AC6C1400ACA8 /* PBXBuildRule */,
			);
			dependencies = (
			);
			name = "moonfield_window (standalone executable)";
			productName = moonfield_window;
			productReference = CA00BD0ECD649217DD73837A /* moonfield_window */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CAF32D43DE71E04653AD465F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				TargetAttributes = {
					CA00BD0ECD644581F30937FB = {
						CreatedOnToolsVersion = 9.2;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = CAF62D43DE7180E02D6C7F57 /* Build configuration list for PBXProject "moonfield-window-example" */;
			compatibilityVersion = "Xcode 11.4";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CAF02D43DE71D65BC3C892A8;
			productRefGroup = CAF12D43DE7122869D176AE5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CA00BD0ECD644581F30937FB /* moonfield_window (standalone executable) */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		CA0009C2BFEC4581F30937FB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CA00FBD8F20D4581F30937FB /* Cargo.toml in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		CA008D0E299D4581F30937FB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CARGO_XCODE_CARGO_DEP_FILE_NAME = moonfield_window.d;
				CARGO_XCODE_CARGO_FILE_NAME = moonfield_window;
				PRODUCT_NAME = moonfield_window;
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Release;
		};
		CA00677EB7FC4581F30937FB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CARGO_XCODE_CARGO_DEP_FILE_NAME = moonfield_window.d;
				CARGO_XCODE_CARGO_FILE_NAME = moonfield_window;
				PRODUCT_NAME = moonfield_window;
				SUPPORTED_PLATFORMS = macosx;
			};
			name = Debug;
		};
		CAF724E205C33CC16B37690B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CARGO_TARGET_DIR = "$(PROJECT_TEMP_DIR)/cargo_target";
				CARGO_XCODE_BUILD_PROFILE = release;
				CARGO_XCODE_FEATURES = "";
				CURRENT_PROJECT_VERSION = 0.1;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				MARKETING_VERSION = 0.1.0;
				PRODUCT_NAME = "moonfield-window-example";
				RUSTUP_TOOLCHAIN = "";
				SDKROOT = macosx;
				SUPPORTS_MACCATALYST = YES;
			};
			name = Release;
		};
		CAF824E205C3228BE02872F8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CARGO_TARGET_DIR = "$(PROJECT_TEMP_DIR)/cargo_target";
				CARGO_XCODE_BUILD_PROFILE = debug;
				CARGO_XCODE_FEATURES = "";
				CURRENT_PROJECT_VERSION = 0.1;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				MARKETING_VERSION = 0.1.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "moonfield-window-example";
				RUSTUP_TOOLCHAIN = "";
				SDKROOT = macosx;
				SUPPORTS_MACCATALYST = YES;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CA00836429264581F30937FB /* Build configuration list for PBXNativeTarget "moonfield_window (standalone executable)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CA008D0E299D4581F30937FB /* Release */,
				CA00677EB7FC4581F30937FB /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CAF62D43DE7180E02D6C7F57 /* Build configuration list for PBXProject "moonfield-window-example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CAF724E205C33CC16B37690B /* Release */,
				CAF824E205C3228BE02872F8 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = CAF32D43DE71E04653AD465F /* Project object */;
}
